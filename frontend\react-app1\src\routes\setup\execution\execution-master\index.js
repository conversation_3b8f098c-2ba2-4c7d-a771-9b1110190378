import React, { useState, useCallback, useEffect } from 'react';
import {
    Select,
    Alert,
    Form,
    Button,
    Row,
    Card,
    Collapse,
    Spin,
    message,
    Badge,
    Tag,
    Input,
    Col,
    Divider,
    Space,
} from 'antd';
import {
    SearchOutlined,
    FilterOutlined,
    DownOutlined,
    UpOutlined,
} from '@ant-design/icons';

import Widget from '../../../../components/Widget';
import FormBuilder from 'antd-form-builder';
import http_utils from '../../../../util/http_utils';
import CircularProgress from '../../../../components/CircularProgress';
import {
    convertDateFieldsToMoments,
    getTouchedFieldsValueInForm,
    priorities,
    convertUTCToDisplayTime,
    NoData,
    getReminders,
} from '../../../../util/helpers';
import TimePickerWidget from '../../../../components/wify-utils/TimePickerWidget';
import './ExecutionMaster.css';
const { Panel } = Collapse;
const protoUrl = '/setup/execution/execution-master/proto';
const subtmitUrl = '/setup/execution/execution-master';

const dummyFormData = {
    vertical_list: 'assembly_installation',
    brand: 'furniture_brand',
    execution_mode: 'Product_wise',

    // Category defaults section
    category_defaults: {
        assembly_installation: {
            skill_1: 'basic_assembly',
            skill_2: 'advanced_assembly',
            skill_3: 'electrical_work',
            manpower_1: '2',
            manpower_2: '3',
            manpower_3: '1',
            duration_1: '30min',
            duration_2: '45min',
            duration_3: '60min',
        },
        smart_solutions: {
            skill_1: 'smart_device_setup',
            skill_2: 'plumbing',
            skill_3: 'carpentry',
            manpower_1: '1',
            manpower_2: '2',
            manpower_3: '1',
            duration_1: '20min',
            duration_2: '35min',
            duration_3: '50min',
        },
    },

    // Product wise section
    product_wise: {
        assembly_installation: {
            furniture_kit: {
                skill_1: 'basic_assembly',
                skill_2: 'advanced_assembly',
                skill_3: 'plumbing',
                manpower_1: '2',
                manpower_2: '1',
                manpower_3: '1',
                duration_1: '30min',
                duration_2: '20min',
                duration_3: '40min',
            },
            premium_kit: {
                skill_1: 'electrical_work',
                skill_2: 'carpentry',
                skill_3: 'smart_device_setup',
                manpower_1: '3',
                manpower_2: '2',
                manpower_3: '2',
                duration_1: '60min',
                duration_2: '45min',
                duration_3: '90min',
            },
        },
        smart_solutions: {
            smart_system: {
                skill_1: 'smart_device_setup',
                skill_2: 'electrical_work',
                skill_3: 'plumbing',
                manpower_1: '1',
                manpower_2: '2',
                manpower_3: '1',
                duration_1: '25min',
                duration_2: '35min',
                duration_3: '55min',
            },
        },
    },
};

const staticCategories = [
    {
        id: 'assembly_installation',
        name: 'Assembly & Installation',
        products: 2,
        image: 'https://via.placeholder.com/300x150?text=Assembly+Category',
    },
    {
        id: 'smart_solutions',
        name: 'Smart Solutions',
        products: 2,
        image: 'https://via.placeholder.com/300x150?text=Smart+Solutions',
    },
];

const staticCategoriesWithProducts = [
    {
        id: 'assembly_installation',
        name: 'Assembly & Installation',
        products: [
            {
                id: 'furniture_kit',
                name: 'Furniture Assembly Kit',
                sku: 'FAK-001',
                description:
                    'Complete furniture assembly and installation service package',
                serviceType: 'Basic Assembly',
            },
            {
                id: 'premium_kit',
                name: 'Premium Assembly Kit',
                sku: 'PAK-002',
                description:
                    'High-end furniture assembly with premium tools and materials',
                serviceType: 'Premium Assembly',
            },
        ],
    },
    {
        id: 'smart_solutions',
        name: 'Smart Solutions',
        products: [
            {
                id: 'smart_system',
                name: 'Smart Installation System',
                sku: 'SIS-003',
                description: 'Smart home installation service package',
                serviceType: 'Smart Installation',
            },
        ],
    },
];

//[{"category_id":1,"category_name":"Assembly and Installations",
// "product_details":
// [{"sku_code": "sku-1020", "product_id": 13, "description": null, "product_name": "AC"}
//]}]

const skillOptions = [
    { label: 'Basic Assembly', value: 'basic_assembly' },
    { label: 'Advanced Assembly', value: 'advanced_assembly' },
    { label: 'Electrical Work', value: 'electrical_work' },
    { label: 'Plumbing', value: 'plumbing' },
    { label: 'Carpentry', value: 'carpentry' },
    { label: 'Smart Device Setup', value: 'smart_device_setup' },
];

const ExecutionMaster = () => {
    const [viewData, setViewData] = useState(undefined);
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [isFormSubmitting, setIsFormSubmitting] = useState(false);
    const [error, setError] = useState(undefined);
    const [form] = Form.useForm();
    const forceUpdate = FormBuilder.useForceUpdate();
    const [renderHelper, setRenderHelper] = useState(true);

    // Static data for demonstration

    useEffect(() => {
        initViewData();
    }, []);

    const fetchBrandOptions = (verticalId) => {
        if (!isLoadingViewData) {
            setIsLoadingViewData(true);
            setViewData(undefined);
            setError(undefined);
            var params = {};
            const onComplete = (resp) => {
                setIsLoadingViewData(false);
                setViewData(resp.data);
                setTimeout(() => {
                    forceUpdate();
                }, 200);
            };
            const onError = (error) => {
                setIsLoadingViewData(false);
                setError(http_utils.decodeErrorToMessage(error));
            };
            http_utils.performGetCall(
                protoUrl + '/' + verticalId,
                params,
                onComplete,
                onError
            );
        }
    };


    const initViewData = () => {
        if (!isLoadingViewData) {
            setIsLoadingViewData(true);
            setViewData(undefined);
            setError(undefined);
            var params = {};
            const onComplete = (resp) => {
                setIsLoadingViewData(false);
                setViewData(resp.data);
                setTimeout(() => {
                    forceUpdate();
                }, 200);
            };
            const onError = (error) => {
                setIsLoadingViewData(false);
                setError(http_utils.decodeErrorToMessage(error));
            };
            http_utils.performGetCall(protoUrl, params, onComplete, onError);
        }
    };

    const handleFinish = (values) => {
        // Include skills data in the submission
        const submissionData = {
            ...values,
        };
        console.log('Submit: ', submissionData);

        if (!isFormSubmitting) {
            setIsFormSubmitting(true);
            var params = submissionData;
            const onComplete = (resp) => {
                setIsFormSubmitting(false);
                message.success('Saved successfully');
                // setViewData(resp.data);
            };
            const onError = (error) => {
                setIsFormSubmitting(false);
                message.error(http_utils.decodeErrorToMessage(error));
            };
            http_utils.performPostCall(subtmitUrl, params, onComplete, onError);
        }
    };

    // const handleFinish = (values) => {
    //     console.log('Raw form values:', values);

    //     const rows = [];

    //     // Shared IDs
    //     const verticalId = parseInt(values.vertical_list, 10);
    //     const serviceTypeId = parseInt(values.brand, 10); // assuming brand == service_type_id

    //     if (values.execution_mode === 'category_default') {
    //         // loop through categories
    //         Object.entries(values.category_defaults || {}).forEach(
    //             ([categoryId, defaults]) => {
    //                 // find matching category in viewData to get product list
    //                 const category = viewData?.category_wise_product_list.find(
    //                     (c) =>
    //                         parseInt(c.category_id, 10) ===
    //                         parseInt(categoryId, 10)
    //                 );

    //                 if (category && category.product_details) {
    //                     category.product_details.forEach((product) => {
    //                         rows.push({
    //                             vertical_id: verticalId,
    //                             service_type_id: serviceTypeId,
    //                             product_id: parseInt(product.product_id, 10),
    //                             skill_1: defaults.skill_1 || null,
    //                             skill_2: defaults.skill_2 || null,
    //                             skill_3: defaults.skill_3 || null,
    //                             manpower_1: defaults.manpower_1 || null,
    //                             manpower_2: defaults.manpower_2 || null,
    //                             manpower_3: defaults.manpower_3 || null,
    //                             duration_1: defaults.duration_1 || null,
    //                             duration_2: defaults.duration_2 || null,
    //                             duration_3: defaults.duration_3 || null,
    //                         });
    //                     });
    //                 }
    //             }
    //         );
    //     }

    //     if (values.execution_mode === 'Product_wise') {
    //         // loop through categories and products
    //         Object.entries(values.product_wise || {}).forEach(
    //             ([categoryId, products]) => {
    //                 Object.entries(products || {}).forEach(
    //                     ([productId, details]) => {
    //                         rows.push({
    //                             vertical_id: verticalId,
    //                             service_type_id: serviceTypeId,
    //                             product_id: parseInt(productId, 10),
    //                             skill_1: details.skill_1 || null,
    //                             skill_2: details.skill_2 || null,
    //                             skill_3: details.skill_3 || null,
    //                             manpower_1: details.manpower_1 || null,
    //                             manpower_2: details.manpower_2 || null,
    //                             manpower_3: details.manpower_3 || null,
    //                             duration_1: details.duration_1 || null,
    //                             duration_2: details.duration_2 || null,
    //                             duration_3: details.duration_3 || null,
    //                         });
    //                     }
    //                 );
    //             }
    //         );
    //     }

    //     // Bulk upload mode will be handled separately (maybe parse CSV/Excel into same rows array)

    //     console.log('Transformed DB-ready rows:', rows);

    //     if (!isFormSubmitting) {
    //         setIsFormSubmitting(true);
    //         http_utils.performPostCall(
    //             subtmitUrl,
    //             rows, // send rows array directly
    //             () => {
    //                 setIsFormSubmitting(false);
    //                 message.success('Saved successfully');
    //             },
    //             (error) => {
    //                 setIsFormSubmitting(false);
    //                 message.error(http_utils.decodeErrorToMessage(error));
    //             }
    //         );
    //     }
    // };

    const getMeta = () => {
        const verticalList = viewData?.vertical_list;
        const selectedVetial = form?.getFieldValue('vertical_list');
        const selectedBrand = form?.getFieldValue('brand');
        const meta = {
            columns: 4,
            formItemLayout: null, // Must set this for inline layout
            colon: true,
            fields: [
                {
                    key: 'vertical_list',
                    label: 'Select verticals',
                    widget: 'select',
                    widgetProps: {
                        mode: 'single',
                        optionFilterProp: 'children',
                    },
                    onChange: (value) => {
                        form.setFieldsValue({ brand: undefined });
                        form.setFieldsValue({ execution_mode: undefined });
                        forceUpdate();
                        fetchBrandOptions(value);
                    },
                    colSpan: 4,
                    options: verticalList || [],
                },
                //if vertical select then show brand select only
                ...(selectedVetial
                    ? [
                          {
                              key: 'brand',
                              label: 'Select Brand/Service',
                              widget: 'select',
                              widgetProps: {
                                  optionFilterProp: 'children',
                              },
                              options: viewData?.org_list || [],
                              colSpan: 4,
                              onChange: (value) => {
                                  forceUpdate();
                                  fetchProductAndCategoryWiseData(value);
                              },
                          },
                      ]
                    : []),

               

                //...singleRoleMeta,
            ],
        };

        return meta;
    };

  

    const meta = getMeta();

    const prefillFormData = convertDateFieldsToMoments(
        viewData?.form_data || {},
        meta.fields
    );
    // console.log('prefillFormData',prefillFormData);
    return (
        <div>
            {isLoadingViewData ? (
                <div className="gx-loader-view gx-loader-position">
                    <CircularProgress />
                </div>
            ) : viewData == undefined ? (
                <p className="gx-text-red">{error}</p>
            ) : (
                <Widget>
                    <>
                        <h2 className="gx-my-1">Execution Master</h2>
                        {/* <Alert
                            message="About Pulse Tracker..."
                            description="To make sure that a user is working today or absent, which we call as PULSE."
                            type="info"
                            showIcon
                        /> */}
                        <Form
                            form={form}
                            layout="vertical"
                            onFinish={handleFinish}
                            initialValues={prefillFormData}
                        >
                            <FormBuilder meta={meta} form={form} />

                           

                            <Form.Item
                                className="form-footer"
                                style={{ marginTop: 24 }}
                            >
                                {isFormSubmitting && <Spin />}
                                <Button
                                    htmlType="submit"
                                    type="primary"
                                    className="gx-mb-0"
                                    disabled={isFormSubmitting}
                                >
                                    Submit
                                </Button>
                            </Form.Item>
                        </Form>
                    </>
                </Widget>
            )}
        </div>
    );
};

export default ExecutionMaster;
