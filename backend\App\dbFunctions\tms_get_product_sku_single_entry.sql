CREATE OR REPLACE FUNCTION public.tms_get_product_sku_single_entry(form_data_ json, entry_id_ bigint)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	--Declaration
declare  
	status boolean;
	message text;
	resp_data json;
	org_id_ integer;
    filter_proto json;
	category_list json;

begin
		
	status = false;
	message = 'Internal_error';
    filter_proto ='{}'::json;

	org_id_ = (form_data_->>'org_id')::int;
   
    category_list = array_to_json(array(
		    	select jsonb_build_object(
		    			'value',category.db_id  ,
		    			'title',category.category_name ,
		    			'label',category.category_name ,
		    			'is_active',category.is_active 
		    		   ) 
			      from cl_tx_category as category
			     where category.org_id = org_id_
	 ));
     if json_array_length(category_list) > 0 then 
		    filter_proto = jsonb_set(filter_proto::jsonb,'{category_list}',category_list::jsonb,true);
	 end if;

    -- ✅ Handle case when no entry_id but still need category list
    if entry_id_ = 0 then 
        status := true;
        message := 'success';
        return jsonb_build_object(
            'status',status,
            'code',message,
            'data', jsonb_build_object('filters_proto', filter_proto)
        );    	
    end if;

	resp_data = array_to_json(array( 
			select jsonb_build_object(	
						'product_id',product_sku_.db_id,
						'sku_code',product_sku_.sku,
						'product_name',product_sku_.product_name,
						'product_description',product_sku_.description,
						'does_has_serial_no',product_sku_.does_has_serial_no,
						'price',product_sku_.price,
						'status',case 
									when product_sku_.is_active then
										'Active'
									else 
										'Inactive'
									end,
						'is_active',product_sku_.is_active,
						'created_on',(product_sku_.c_meta).time,
						'last_u_date',(product_sku_.u_meta).time,
						'is_active_state_change_by',case 
														when (product_sku_.u_by is null) then 
															product_sku_.c_by  
														else 
															product_sku_.u_by  
														end,
						'c_by', product_sku_.c_by  ,
						'form_data',product_sku_.form_data,
						'filters_proto', filter_proto
					)	
		      from cl_tx_product_sku as product_sku_	          
			 where product_sku_.org_id = org_id_
			   and product_sku_.db_id = entry_id_
			));
	
	if json_array_length(resp_data) > 0 then 
		status = true;
		message = 'success';
	end if;

	return jsonb_build_object('status',status,'code',message,'data',resp_data->0);

	END;
$function$
;
