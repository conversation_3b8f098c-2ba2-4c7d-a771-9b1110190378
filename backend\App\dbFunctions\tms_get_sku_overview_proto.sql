CREATE OR REPLACE FUNCTION public.tms_get_sku_overview_proto(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$

-- Declarations
declare 
	status boolean;
 	message text;
 	
 	org_id_ integer;
 	usr_id_ uuid;
   
   --temp
    resp_data json;
    filter_proto json;
    product_list json;
    active_product_list json;
    config_data json;

begin
		
	status = false;
	message = 'Internal_error';
	resp_data = '{}'::json;
    config_data ='{}'::json;
	
	--form data 
	org_id_ = json_extract_path_text(form_data,'org_id');
	usr_id_ = json_extract_path_text(form_data,'usr_id');

   
    config_data = tms_hlpr_get_setting_details(form_data,'PARTS_MANAGEMENT_CUSTOM_FIELDS')->'data'->>'form_data';
   
    if config_data is not null then 
    	resp_data = jsonb_set(resp_data::jsonb,'{config_data}',config_data::jsonb,true);
    end if;
    

--By default
	status = true;
	message = 'success';

   
   return json_build_object('status',status,'code',message,'data',resp_data);
   

end;
$function$
;
