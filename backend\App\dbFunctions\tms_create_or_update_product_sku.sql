CREATE OR REPLACE FUNCTION public.tms_create_or_update_product_sku(form_data_ json, entry_id integer DEFAULT 0)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare
		resp_data json;
		message text;
		status boolean;

		org_id_ int;
		ins_id integer;
		affected_rows integer;
		usr_id_ uuid;
		ip_address_ text;
		user_agent_ text;
		sku_ text;
		product_name_ text;
		description_ text;
		price_ numeric;
		does_has_serial_no_ bool default false;
		does_has_serial_no_status text;
		is_active_ bool default false;
		is_active_status text;
		validation_resp_ json;
		category_id_ int;

	BEGIN
		message = 'Internal_error';
		status = false;		
	
		usr_id_ = form_data_->>'usr_id';
		org_id_ = form_data_->'org_id';
	
		sku_ = form_data_->>'sku_code';
		product_name_ = form_data_->>'product_name';
		description_ = form_data_->>'product_description';
		price_ = form_data_->'price';
		ip_address_ = form_data_->>'ip_address';
		user_agent_ = form_data_->>'user_agent';
		is_active_status = form_data_->>'is_active';
		does_has_serial_no_status = form_data_->>'does_has_serial_no';
		category_id_ = form_data_->>'category_id';
	
		if is_active_status = 'true' or UPPER(is_active_status) = 'ACTIVE' or is_active_status = 'Yes' then 
			is_active_ = true;		
		end if;
	
		if does_has_serial_no_status = 'true' or UPPER(does_has_serial_no_status) = 'ACTIVE' or does_has_serial_no_status = 'Yes' then 
			does_has_serial_no_ = true;
		end if;

		validation_resp_ = tms_hlpr_product_sku_validations(form_data_,entry_id);			
	  	if (validation_resp_->>'status')::bool is not true then
	  		return json_build_object('status',status,'code',validation_resp_->>'code','data', validation_resp_->>'message');
	  	end if;
		  
		if entry_id = 0 then					
			
			insert into public.cl_tx_product_sku (
				org_id, sku, product_name, does_has_serial_no, price, description, is_active, c_meta, c_by, form_data, category
				)
				values (
					 org_id_, 
					 sku_,
					 product_name_,
					 case 
					 	when does_has_serial_no_status is null then
					 		true
					 	else						 	
				 			does_has_serial_no_
					 end,
					 price_,
					 description_,
					 case 
					 	when is_active_status is null then
					 		true
					 	else						 	
				 			is_active_
					 end,				
					 row(ip_address_,user_agent_,now() at time zone 'utc'), 
					 usr_id_,
					 form_data_,
					 category_id_
				)
				returning db_id into ins_id;
			get diagnostics affected_rows = ROW_COUNT;
			
			if affected_rows > 0 then		
				message = 'success';
				status = true;
				resp_data =  json_build_object('entry_id',ins_id);		
			end if;
		elsif entry_id > 0 then
		
			update public.cl_tx_product_sku as product_skus
			   set u_meta = row(ip_address_,user_agent_,now() at time zone 'utc'),
		           u_by = usr_id_,
		           product_name = product_name_,
		           does_has_serial_no = does_has_serial_no_,
		           price = price_,
		           description = description_,		                 
		           is_active = case 
			           			when is_active_status is not null then 
			           				is_active_
			           			else 
			           				is_active  
		           			  end,
       			   form_data = ((form_data)::jsonb || (form_data_)::jsonb)::json,	  
				   category = category_id_    
			 where product_skus.org_id = org_id_
		       and product_skus.db_id = entry_id;
			
			get diagnostics affected_rows = ROW_COUNT;
			
			if affected_rows > 0 then	
				message = 'success';
				status = true;
				resp_data =  json_build_object('entry_id',entry_id);
			end if;  
		end if;
		return jsonb_build_object('status',status,'code',message,'data',resp_data);
	END;
$function$
;
